"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Save, AlertTriangle } from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";

export function MaterialForm() {
  const [formData, setFormData] = useState({
    name: "",
    length: "",
    width: "",
    thickness: "18",
    unit: "MM",
    quantity: "1",
    cost: "1.0",
    supplier: "unknown",
    notes: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const router = useRouter();
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    setError("");
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    // Validation
    if (!formData.name.trim()) {
      setError("Material name is required");
      setLoading(false);
      return;
    }

    const length = parseFloat(formData.length);
    if (isNaN(length) || length <= 0) {
      setError("Length must be a valid positive number");
      setLoading(false);
      return;
    }

    const width = parseFloat(formData.width);
    if (isNaN(width) || width <= 0) {
      setError("Width must be a valid positive number");
      setLoading(false);
      return;
    }

    let thickness = null;
    if (formData.thickness.trim()) {
      thickness = parseFloat(formData.thickness);
      if (isNaN(thickness) || thickness <= 0) {
        setError("Thickness must be a valid positive number");
        setLoading(false);
        return;
      }
    }

    const quantity = parseInt(formData.quantity);
    if (isNaN(quantity) || quantity <= 0) {
      setError("Quantity must be a valid positive integer");
      setLoading(false);
      return;
    }

    let cost = null;
    if (formData.cost.trim()) {
      cost = parseFloat(formData.cost);
      if (isNaN(cost) || cost < 0) {
        setError("Cost must be a valid non-negative number");
        setLoading(false);
        return;
      }
    }

    try {
      const response = await fetch("/api/materials", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          length: length,
          width: width,
          thickness: thickness,
          unit: formData.unit,
          quantity: quantity,
          cost: cost,
          supplier: formData.supplier.trim() || null,
          notes: formData.notes.trim() || null,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create material");
      }

      if (result.success) {
        toast({
          title: "Material added",
          description:
            "Your material has been added to inventory successfully.",
        });
        router.push("/dashboard/inventory");
        router.refresh();
      } else {
        throw new Error(result.error || "Failed to create material");
      }
    } catch (err) {
      console.error("Error creating material:", err);
      setError(
        err instanceof Error ? err.message : "Failed to create material",
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/dashboard/inventory">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Inventory
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Add New Material</h1>
          <p className="text-gray-600 mt-1">
            Add a new material to your inventory
          </p>
        </div>
      </div>

      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle>Material Information</CardTitle>
            <CardDescription>
              Enter the details for your new material
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">Material Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="e.g., Plywood 18mm, Oak Board, MDF Sheet"
                  disabled={loading}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="length">Length *</Label>
                  <Input
                    id="length"
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.length}
                    onChange={(e) =>
                      handleInputChange("length", e.target.value)
                    }
                    placeholder="2440"
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="width">Width *</Label>
                  <Input
                    id="width"
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.width}
                    onChange={(e) => handleInputChange("width", e.target.value)}
                    placeholder="1220"
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="thickness">Thickness</Label>
                  <Input
                    id="thickness"
                    type="number"
                    step="0.1"
                    min="0"
                    value={formData.thickness}
                    onChange={(e) =>
                      handleInputChange("thickness", e.target.value)
                    }
                    placeholder="18"
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="unit">Unit</Label>
                  <Select
                    value={formData.unit}
                    onValueChange={(value) => handleInputChange("unit", value)}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MM">Millimeters (MM)</SelectItem>
                      <SelectItem value="CM">Centimeters (CM)</SelectItem>
                      <SelectItem value="M">Meters (M)</SelectItem>
                      <SelectItem value="IN">Inches (IN)</SelectItem>
                      <SelectItem value="FT">Feet (FT)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity *</Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="1"
                    value={formData.quantity}
                    onChange={(e) =>
                      handleInputChange("quantity", e.target.value)
                    }
                    placeholder="1"
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cost">Cost per Unit</Label>
                  <Input
                    id="cost"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.cost}
                    onChange={(e) => handleInputChange("cost", e.target.value)}
                    placeholder="25.99"
                    disabled={loading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="supplier">Supplier</Label>
                  <Input
                    id="supplier"
                    value={formData.supplier}
                    onChange={(e) =>
                      handleInputChange("supplier", e.target.value)
                    }
                    placeholder="e.g., Home Depot, Local Lumber Yard"
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Additional notes about this material..."
                  disabled={loading}
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button type="button" variant="outline" asChild>
                  <Link href="/dashboard/inventory">Cancel</Link>
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    "Adding..."
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Add Material
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
